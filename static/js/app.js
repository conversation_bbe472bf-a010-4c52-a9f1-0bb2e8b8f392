// 键盘鼠标统计应用主逻辑
class ClickStatsApp {
    constructor() {
        this.dailyChart = null;
        this.currentDays = 30;

        // 按键显示名称映射
        this.keyDisplayNames = {
            'esc': 'Esc',
            'f1': 'F1',
            'f2': 'F2',
            'f3': 'F3',
            'f4': 'F4',
            'f5': 'F5',
            'f6': 'F6',
            'f7': 'F7',
            'f8': 'F8',
            'f9': 'F9',
            'f10': 'F10',
            'f11': 'F11',
            'f12': 'F12',
            'backspace': '←',
            'tab': 'Tab',
            'caps_lock': 'Caps',
            'enter': '↵',
            'shift': 'Shift',
            'shift_l': 'L Shift',
            'shift_r': 'R Shift',
            'ctrl': 'Ctrl',
            'ctrl_l': 'L Ctrl',
            'ctrl_r': 'R Ctrl',
            'alt': 'Alt',
            'alt_l': 'L Alt',
            'alt_r': 'R Alt',
            'cmd': 'Cmd',
            'cmd_l': 'L Cmd',
            'cmd_r': 'R Cmd',
            'fn': 'Fn',
            'win': '⊞',
            'space': 'Space',
            '\\': '\\',
            "'": "'",
            ';': ';',
            '[': '[',
            ']': ']',
            ',': ',',
            '.': '.',
            '/': '/',
            '-': '-',
            '=': '=',
            'home': 'Home',
            'end': 'End',
            'page_up': 'PgUp',
            'page_down': 'PgDn',
            'insert': 'Ins',
            'delete': 'Del',
            'up': '↑',
            'down': '↓',
            'left': '←',
            'right': '→'
        };

        this.init();
    }

    // 初始化应用
    init() {
        this.setupEventListeners();
        this.loadData();
        this.createKeyboardLayout();
        this.createMouseLayout();
    }

    // 设置事件监听器
    setupEventListeners() {
        document.getElementById('refreshBtn').addEventListener('click', () => {
            this.loadData();
        });

        document.getElementById('daySelector').addEventListener('change', (e) => {
            this.currentDays = parseInt(e.target.value);
            this.loadData();
        });
    }

    // 显示加载指示器
    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'block';
    }

    // 隐藏加载指示器
    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    // 显示错误信息
    showError(message) {
        const errorElement = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        errorText.textContent = message;
        errorElement.style.display = 'flex';
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }

    // 加载所有数据
    async loadData() {
        this.showLoading();
        try {
            await Promise.all([
                this.loadSummary(),
                this.loadDailyStats(),
                this.loadKeyboardHeatmap(),
                this.loadMouseHeatmap()
            ]);
        } catch (error) {
            console.error('数据加载失败:', error);
            this.showError('数据加载失败，请稍后重试');
        } finally {
            this.hideLoading();
        }
    }

    // 加载统计摘要
    async loadSummary() {
        try {
            const response = await fetch('/api/summary');
            const result = await response.json();
            
            if (result.success) {
                this.updateSummaryCards(result.data);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('加载摘要数据失败:', error);
        }
    }

    // 更新统计卡片
    updateSummaryCards(data) {
        document.getElementById('todayKeyboard').textContent = data.today_keyboard_clicks.toLocaleString();
        document.getElementById('todayMouse').textContent = data.today_mouse_clicks.toLocaleString();
        document.getElementById('todayTotal').textContent = data.today_total_clicks.toLocaleString();
        document.getElementById('mostActiveKey').textContent = 
            `${data.most_active_key.key} (${data.most_active_key.count})`;
    }

    // 加载每日统计数据
    async loadDailyStats() {
        try {
            const response = await fetch(`/api/daily-stats?days=${this.currentDays}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateDailyChart(result.data);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('加载每日统计失败:', error);
        }
    }

    // 更新每日统计图表
    updateDailyChart(data) {
        const ctx = document.getElementById('dailyChart').getContext('2d');
        
        // 准备图表数据
        const sortedData = data.sort((a, b) => new Date(a.stat_date) - new Date(b.stat_date));
        const labels = sortedData.map(item => {
            const date = new Date(item.stat_date);
            return `${date.getMonth() + 1}/${date.getDate()}`;
        });
        const keyboardData = sortedData.map(item => item.total_keyboard_clicks || 0);
        const mouseData = sortedData.map(item => item.total_mouse_clicks || 0);

        // 销毁现有图表
        if (this.dailyChart) {
            this.dailyChart.destroy();
        }

        // 创建新图表
        this.dailyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '键盘点击',
                        data: keyboardData,
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: '鼠标点击',
                        data: mouseData,
                        borderColor: '#2ecc71',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 6,
                        hoverRadius: 8
                    }
                }
            }
        });
    }

    // 加载键盘热力图数据
    async loadKeyboardHeatmap() {
        try {
            const response = await fetch(`/api/keyboard-heatmap?days=${this.currentDays}`);
            const result = await response.json();

            if (result.success) {
                this.updateKeyboardHeatmap(result.data);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('加载键盘热力图失败:', error);
        }
    }

    // 加载鼠标热力图数据
    async loadMouseHeatmap() {
        try {
            const response = await fetch(`/api/mouse-heatmap?days=${this.currentDays}`);
            const result = await response.json();

            if (result.success) {
                this.updateMouseHeatmap(result.data);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('加载鼠标热力图失败:', error);
        }
    }

    // 创建键盘布局
    createKeyboardLayout() {
        const keyboardContainer = document.getElementById('keyboardHeatmap');

        // 完整键盘布局定义
        const keyboardLayout = {
            functionRow: [
                ['esc', 'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12', 'insert', 'delete', 'home', 'end']
            ],
            main: [
                ['`', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', 'backspace'],
                ['tab', 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
                ['caps_lock', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'", 'enter'],
                ['shift_l', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/', 'shift_r'],
                ['ctrl_l', 'fn', 'win', 'alt_l', 'space', 'alt_r', 'ctrl_r', 'page_up', 'up', 'page_down'],
                ['', '', '', '', '', '', '', '', '', 'left', 'down', 'right']
            ]
        };



        keyboardContainer.innerHTML = '';

        // 创建功能键行
        if (keyboardLayout.functionRow) {
            keyboardLayout.functionRow.forEach(row => {
                const rowDiv = document.createElement('div');
                rowDiv.className = 'keyboard-row function-row';

                row.forEach(keyName => {
                    const keyDiv = document.createElement('div');
                    keyDiv.className = `key function-key ${keyName}`;

                    const displayName = this.keyDisplayNames[keyName] || keyName.toUpperCase();
                    keyDiv.textContent = displayName;
                    keyDiv.dataset.key = keyName;
                    keyDiv.title = `${keyName}: 0 次点击`;

                    rowDiv.appendChild(keyDiv);
                });

                keyboardContainer.appendChild(rowDiv);
            });
        }

        // 创建主键盘区域
        const mainKeyboard = document.createElement('div');
        mainKeyboard.className = 'keyboard-main';

        keyboardLayout.main.forEach(row => {
            const rowDiv = document.createElement('div');
            rowDiv.className = 'keyboard-row';

            row.forEach(keyName => {
                if (keyName === '') {
                    // 创建空白占位符
                    const spacerDiv = document.createElement('div');
                    spacerDiv.className = 'key-spacer';
                    rowDiv.appendChild(spacerDiv);
                } else {
                    const keyDiv = document.createElement('div');
                    keyDiv.className = `key ${keyName}`;

                    // 使用映射的显示名称或转换为大写
                    const displayName = this.keyDisplayNames[keyName] || keyName.toUpperCase();
                    keyDiv.textContent = displayName;
                    keyDiv.dataset.key = keyName;

                    // 添加点击提示
                    keyDiv.title = `${keyName}: 0 次点击`;

                    rowDiv.appendChild(keyDiv);
                }
            });

            mainKeyboard.appendChild(rowDiv);
        });



        keyboardContainer.appendChild(mainKeyboard);
    }

    // 创建鼠标布局
    createMouseLayout() {
        const mouseContainer = document.getElementById('mouseHeatmap');

        mouseContainer.innerHTML = `
            <div class="mouse-container">
                <div class="mouse-buttons">
                    <div class="mouse-button" data-button="left" title="左键: 0 次点击">L</div>
                    <div class="mouse-button" data-button="middle" title="中键: 0 次点击">M</div>
                    <div class="mouse-button" data-button="right" title="右键: 0 次点击">R</div>
                </div>
                <div class="mouse-scroll">
                    <div class="scroll-direction" data-button="scroll_up" title="向上滚动: 0 次">↑</div>
                    <div class="scroll-direction" data-button="scroll_down" title="向下滚动: 0 次">↓</div>
                </div>
            </div>
            <div class="mouse-stats">
                <div class="stat-item" id="mouseClickTotal">总点击: 0</div>
                <div class="stat-item" id="mouseScrollTotal">滚动: 0</div>
            </div>
        `;
    }

    // 更新键盘热力图
    updateKeyboardHeatmap(data) {
        const keys = document.querySelectorAll('.key');
        const maxClicks = Math.max(...Object.values(data), 1);

        console.log('热力图数据:', data); // 调试信息

        keys.forEach(keyElement => {
            const keyName = keyElement.dataset.key;
            let clickCount = data[keyName] || 0;

            // 处理兼容性：如果没有找到左右分离的数据，尝试查找合并的数据
            if (clickCount === 0) {
                if (keyName.endsWith('_l') || keyName.endsWith('_r')) {
                    const baseKey = keyName.replace(/_[lr]$/, '');
                    clickCount = data[baseKey] || 0;
                    console.log(`${keyName} -> ${baseKey}: ${clickCount}`); // 调试信息
                }
                // 反向兼容：如果前端期望分离键位，但数据库中是合并的
                else if (['shift', 'ctrl', 'alt', 'cmd'].includes(keyName)) {
                    // 检查是否有对应的左右键位数据
                    const leftKey = `${keyName}_l`;
                    const rightKey = `${keyName}_r`;
                    const leftCount = data[leftKey] || 0;
                    const rightCount = data[rightKey] || 0;
                    if (leftCount > 0 || rightCount > 0) {
                        clickCount = leftCount + rightCount;
                    }
                }
            }

            const heatLevel = Math.min(Math.floor((clickCount / maxClicks) * 9), 9);

            // 移除所有热力等级类
            keyElement.className = keyElement.className.replace(/heat-\d+/g, '');

            // 添加新的热力等级类
            keyElement.classList.add(`heat-${heatLevel}`);

            // 更新提示信息
            const displayName = this.keyDisplayNames[keyName] || keyName;
            keyElement.title = `${displayName}: ${clickCount.toLocaleString()} 次点击`;

            // 调试信息：显示有数据的键
            if (clickCount > 0) {
                console.log(`${keyName}: ${clickCount} 次点击`);
            }
        });
    }

    // 更新鼠标热力图
    updateMouseHeatmap(data) {
        const buttons = document.querySelectorAll('[data-button]');
        const maxClicks = Math.max(...Object.values(data), 1);

        let totalClicks = 0;
        let totalScroll = 0;

        buttons.forEach(buttonElement => {
            const buttonName = buttonElement.dataset.button;
            const clickCount = data[buttonName] || 0;
            const heatLevel = Math.min(Math.floor((clickCount / maxClicks) * 9), 9);

            // 统计总数
            if (buttonName.includes('scroll')) {
                totalScroll += clickCount;
            } else {
                totalClicks += clickCount;
            }

            // 移除所有热力等级类
            buttonElement.className = buttonElement.className.replace(/heat-\d+/g, '');

            // 添加新的热力等级类
            buttonElement.classList.add(`heat-${heatLevel}`);

            // 更新提示信息
            const buttonText = buttonName.replace('_', ' ');
            buttonElement.title = `${buttonText}: ${clickCount.toLocaleString()} 次点击`;
        });

        // 更新统计信息
        const clickTotalElement = document.getElementById('mouseClickTotal');
        const scrollTotalElement = document.getElementById('mouseScrollTotal');

        if (clickTotalElement) {
            clickTotalElement.textContent = `总点击: ${totalClicks.toLocaleString()}`;
        }
        if (scrollTotalElement) {
            scrollTotalElement.textContent = `滚动: ${totalScroll.toLocaleString()}`;
        }
    }

    // 获取热力图颜色
    getHeatColor(value, maxValue) {
        if (value === 0) return '#ecf0f1';

        const intensity = value / maxValue;
        const colors = [
            '#3498db', '#2980b9', '#27ae60', '#f39c12',
            '#e67e22', '#e74c3c', '#c0392b', '#9b59b6', '#8e44ad'
        ];

        const index = Math.min(Math.floor(intensity * colors.length), colors.length - 1);
        return colors[index];
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ClickStatsApp();
});
