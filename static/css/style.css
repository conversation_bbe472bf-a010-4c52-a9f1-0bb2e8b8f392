/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

.header h1 i {
    color: #3498db;
    margin-right: 10px;
}

.header-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.day-selector {
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.day-selector:focus {
    outline: none;
    border-color: #3498db;
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(45deg, #f39c12, #e67e22);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-content p {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

/* 图表区域 */
.charts-section {
    margin-bottom: 30px;
}

.chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
}

.chart-container canvas {
    max-height: 400px !important;
    height: 400px !important;
}

.chart-wrapper {
    position: relative;
    height: 400px;
    width: 100%;
}

.chart-container h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 600;
}

.chart-container h2 i {
    color: #3498db;
    margin-right: 10px;
}

/* 热力图区域 */
.heatmap-section {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
    align-items: start;
}

.heatmap-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 热力图头部 */
.heatmap-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.heatmap-container h2 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.heatmap-container h2 i {
    margin-right: 10px;
}

/* 热力图图例 */
.heatmap-legend {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #2c3e50;
    font-weight: 500;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.2);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.legend-label {
    white-space: nowrap;
    font-size: 11px;
}

/* 键盘布局 */
.keyboard-layout {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
    margin: 0 auto;
    padding: 15px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    border: 2px solid rgba(0, 0, 0, 0.1);
}

.keyboard-row {
    display: flex;
    gap: 4px;
    justify-content: center;
    align-items: center;
}

.keyboard-main {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* 功能键行 */
.function-row {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.function-key {
    min-width: 35px;
    width: 35px;
    height: 32px;
    font-size: 9px;
    font-weight: 700;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    color: #2c3e50;
    border: 1px solid rgba(0, 0, 0, 0.3);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.function-key:hover {
    background: linear-gradient(145deg, #e9ecef, #dee2e6);
    transform: translateY(-1px);
}

/* ESC键和F键使用默认样式，通过热力图颜色显示点击次数 */
.key.esc, .key.f1, .key.f2, .key.f3, .key.f4, .key.f5, .key.f6,
.key.f7, .key.f8, .key.f9, .key.f10, .key.f11, .key.f12 {
    /* 继承默认的热力图颜色，不设置固定背景色 */
    font-weight: 700;
}

/* 键盘占位符 */
.key-spacer {
    min-width: 32px;
    width: 32px;
    height: 38px;
    flex-shrink: 0;
}

.key {
    min-width: 38px;
    width: 38px;
    height: 38px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 700;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.key:hover {
    transform: scale(1.1);
    z-index: 10;
}

.key.space {
    min-width: 190px;
    width: 190px;
}

.key.enter {
    min-width: 75px;
    width: 75px;
}

.key.shift {
    min-width: 75px;
    width: 75px;
    font-size: 9px;
    font-weight: 700;
}

.key.shift_l {
    min-width: 95px;
    width: 95px;
    font-size: 9px;
    font-weight: 700;
}

.key.shift_r {
    min-width: 95px;
    width: 95px;
    font-size: 9px;
    font-weight: 700;
}

.key.backspace {
    min-width: 75px;
    width: 75px;
    font-size: 9px;
    font-weight: 700;
}

.key.tab {
    min-width: 75px;
    width: 75px;
    font-size: 10px;
    font-weight: 700;
}

.key.caps_lock {
    min-width: 75px;
    width: 75px;
    font-size: 9px;
    font-weight: 700;
}

.key.ctrl, .key.alt, .key.fn,
.key.ctrl_l, .key.ctrl_r, .key.alt_l, .key.alt_r,
.key.cmd_l, .key.cmd_r {
    min-width: 45px;
    width: 45px;
    font-size: 9px;
    font-weight: 700;
}

/* 左右键位特殊标识 */
.key.shift_l, .key.ctrl_l, .key.alt_l, .key.cmd_l {
    border-left: 3px solid rgba(52, 152, 219, 0.9);
    box-shadow: inset 3px 0 0 rgba(52, 152, 219, 0.3);
}

.key.shift_r, .key.ctrl_r, .key.alt_r, .key.cmd_r {
    border-right: 3px solid rgba(231, 76, 60, 0.9);
    box-shadow: inset -3px 0 0 rgba(231, 76, 60, 0.3);
}

.key.win {
    min-width: 45px;
    width: 45px;
    font-size: 10px;
    font-weight: 700;
}

/* 方向键样式 */
.key.up, .key.down, .key.left, .key.right {
    min-width: 38px;
    width: 38px;
    height: 38px;
    font-size: 14px;
    font-weight: 700;
}

/* 功能键样式 */
.key.home, .key.end, .key.page_up, .key.page_down, .key.insert, .key.delete {
    min-width: 38px;
    width: 38px;
    font-size: 9px;
    font-weight: 700;
}

/* 鼠标布局 */
.mouse-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.mouse-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    background: #2c3e50;
    border-radius: 25px;
    padding: 15px 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.mouse-buttons {
    display: flex;
    gap: 3px;
}

.mouse-button {
    width: 45px;
    height: 60px;
    border-radius: 20px 20px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.mouse-button:hover {
    transform: scale(1.05);
}

.mouse-scroll {
    width: 35px;
    height: 80px;
    border-radius: 18px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    background: #34495e;
    margin-top: 5px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.scroll-direction {
    width: 25px;
    height: 25px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.mouse-stats {
    margin-top: 10px;
    text-align: center;
    font-size: 12px;
    color: #2c3e50;
}

.mouse-stats .stat-item {
    margin: 5px 0;
    padding: 5px 10px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    font-weight: 500;
}

/* 热力图颜色等级 */
.heat-0 {
    background: linear-gradient(145deg, #ecf0f1, #d5dbdb);
    color: #2c3e50;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}
.heat-1 {
    background: linear-gradient(145deg, #3498db, #2980b9);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
.heat-2 {
    background: linear-gradient(145deg, #2980b9, #1f618d);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
.heat-3 {
    background: linear-gradient(145deg, #27ae60, #1e8449);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
.heat-4 {
    background: linear-gradient(145deg, #f39c12, #d68910);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
.heat-5 {
    background: linear-gradient(145deg, #e67e22, #ca6f1e);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
.heat-6 {
    background: linear-gradient(145deg, #e74c3c, #cb4335);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
.heat-7 {
    background: linear-gradient(145deg, #c0392b, #a93226);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
.heat-8 {
    background: linear-gradient(145deg, #9b59b6, #8e44ad);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}
.heat-9 {
    background: linear-gradient(145deg, #8e44ad, #7d3c98);
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* 加载指示器 */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e0e0e0;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-message {
    background: #e74c3c;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .heatmap-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .keyboard-layout {
        padding: 5px;
        min-height: 200px;
    }

    .keyboard-row {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding: 2px 0;
    }

    .key-spacer {
        min-width: 32px;
        width: 32px;
        height: 38px;
        flex-shrink: 0;
    }

    .function-key {
        min-width: 28px;
        width: 28px;
        height: 28px;
        font-size: 6px;
    }

    .function-row {
        margin-bottom: 6px;
        padding-bottom: 6px;
    }

    .heatmap-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .heatmap-legend {
        gap: 6px;
    }

    .legend-color {
        width: 16px;
        height: 16px;
    }

    .legend-label {
        font-size: 10px;
    }

    .key {
        min-width: 32px;
        width: 32px;
        height: 32px;
        font-size: 8px;
        flex-shrink: 0;
    }

    .key.space {
        min-width: 120px;
        width: 120px;
    }

    .key.enter {
        min-width: 50px;
        width: 50px;
    }

    .key.shift {
        min-width: 60px;
        width: 60px;
    }

    .key.shift_l, .key.shift_r {
        min-width: 70px;
        width: 70px;
        font-size: 7px;
    }

    .key.backspace {
        min-width: 50px;
        width: 50px;
        font-size: 7px;
    }

    .key.tab {
        min-width: 45px;
        width: 45px;
        font-size: 7px;
    }

    .key.caps_lock {
        min-width: 50px;
        width: 50px;
        font-size: 6px;
    }

    .key.ctrl, .key.alt,
    .key.ctrl_l, .key.ctrl_r, .key.alt_l, .key.alt_r,
    .key.cmd_l, .key.cmd_r {
        min-width: 35px;
        width: 35px;
        font-size: 6px;
    }
}
