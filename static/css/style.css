/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

.header h1 i {
    color: #3498db;
    margin-right: 10px;
}

.header-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.day-selector {
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.day-selector:focus {
    outline: none;
    border-color: #3498db;
}

/* 统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(45deg, #f39c12, #e67e22);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-content p {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

/* 图表区域 */
.charts-section {
    margin-bottom: 30px;
}

.chart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
}

.chart-container canvas {
    max-height: 400px !important;
    height: 400px !important;
}

.chart-wrapper {
    position: relative;
    height: 400px;
    width: 100%;
}

.chart-container h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 600;
}

.chart-container h2 i {
    color: #3498db;
    margin-right: 10px;
}

/* 热力图区域 */
.heatmap-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.heatmap-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.heatmap-container h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 600;
}

.heatmap-container h2 i {
    margin-right: 10px;
}

/* 键盘布局 */
.keyboard-layout {
    display: grid;
    gap: 5px;
    width: 100%;
    margin: 0 auto;
    overflow-x: auto;
    padding: 10px;
    min-height: 250px;
}

.keyboard-row {
    display: flex;
    gap: 5px;
    justify-content: center;
    min-width: fit-content;
    flex-shrink: 0;
}

.key {
    min-width: 40px;
    width: 40px;
    height: 40px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.key:hover {
    transform: scale(1.1);
    z-index: 10;
}

.key.space {
    min-width: 180px;
    width: 180px;
}

.key.enter {
    min-width: 70px;
    width: 70px;
}

.key.shift {
    min-width: 80px;
    width: 80px;
}

.key.backspace {
    min-width: 70px;
    width: 70px;
    font-size: 8px;
}

.key.tab {
    min-width: 60px;
    width: 60px;
    font-size: 9px;
}

.key.caps_lock {
    min-width: 70px;
    width: 70px;
    font-size: 8px;
}

.key.ctrl, .key.alt {
    min-width: 50px;
    width: 50px;
    font-size: 9px;
}

/* 鼠标布局 */
.mouse-layout {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.mouse-buttons {
    display: flex;
    gap: 10px;
}

.mouse-button {
    width: 80px;
    height: 100px;
    border-radius: 40px 40px 10px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.mouse-button:hover {
    transform: scale(1.1);
}

.mouse-scroll {
    width: 60px;
    height: 120px;
    border-radius: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    background: #34495e;
    margin-top: 10px;
}

.scroll-direction {
    width: 40px;
    height: 40px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

/* 热力图颜色等级 */
.heat-0 { background: #ecf0f1; color: #2c3e50; }
.heat-1 { background: #3498db; }
.heat-2 { background: #2980b9; }
.heat-3 { background: #27ae60; }
.heat-4 { background: #f39c12; }
.heat-5 { background: #e67e22; }
.heat-6 { background: #e74c3c; }
.heat-7 { background: #c0392b; }
.heat-8 { background: #9b59b6; }
.heat-9 { background: #8e44ad; }

/* 加载指示器 */
.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e0e0e0;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误提示 */
.error-message {
    background: #e74c3c;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .heatmap-section {
        grid-template-columns: 1fr;
    }
    
    .keyboard-layout {
        padding: 5px;
        min-height: 200px;
    }

    .keyboard-row {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding: 2px 0;
    }

    .key {
        min-width: 32px;
        width: 32px;
        height: 32px;
        font-size: 8px;
        flex-shrink: 0;
    }

    .key.space {
        min-width: 120px;
        width: 120px;
    }

    .key.enter {
        min-width: 50px;
        width: 50px;
    }

    .key.shift {
        min-width: 60px;
        width: 60px;
    }

    .key.backspace {
        min-width: 50px;
        width: 50px;
        font-size: 7px;
    }

    .key.tab {
        min-width: 45px;
        width: 45px;
        font-size: 7px;
    }

    .key.caps_lock {
        min-width: 50px;
        width: 50px;
        font-size: 6px;
    }

    .key.ctrl, .key.alt {
        min-width: 40px;
        width: 40px;
        font-size: 7px;
    }
}
