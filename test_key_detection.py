#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pynput库对左右键位的检测能力
"""

from pynput import keyboard
import time

def test_key_detection():
    """测试键位检测"""
    print("键位检测测试")
    print("请按下左Shift、右Shift、左Ctrl、右Ctrl、左Alt、右Alt等键位")
    print("按ESC退出测试")
    print("-" * 50)
    
    def on_press(key):
        try:
            # 打印所有可用的属性
            print(f"按键: {key}")
            print(f"  str(key): {str(key)}")
            
            if hasattr(key, 'name'):
                print(f"  key.name: {key.name}")
            
            if hasattr(key, '_name'):
                print(f"  key._name: {key._name}")
                
            if hasattr(key, 'char'):
                print(f"  key.char: {key.char}")
                
            # 检查是否是特殊键
            if hasattr(keyboard.Key, 'shift_l') and key == keyboard.Key.shift_l:
                print("  -> 检测到左Shift键!")
            elif hasattr(keyboard.Key, 'shift_r') and key == keyboard.Key.shift_r:
                print("  -> 检测到右Shift键!")
            elif hasattr(keyboard.Key, 'ctrl_l') and key == keyboard.Key.ctrl_l:
                print("  -> 检测到左Ctrl键!")
            elif hasattr(keyboard.Key, 'ctrl_r') and key == keyboard.Key.ctrl_r:
                print("  -> 检测到右Ctrl键!")
            elif hasattr(keyboard.Key, 'alt_l') and key == keyboard.Key.alt_l:
                print("  -> 检测到左Alt键!")
            elif hasattr(keyboard.Key, 'alt_r') and key == keyboard.Key.alt_r:
                print("  -> 检测到右Alt键!")
            
            print("-" * 30)
            
            # ESC退出
            if key == keyboard.Key.esc:
                print("退出测试")
                return False
                
        except Exception as e:
            print(f"错误: {e}")
    
    # 启动监听
    with keyboard.Listener(on_press=on_press) as listener:
        listener.join()

if __name__ == "__main__":
    # 首先检查pynput库支持的键位
    print("pynput.keyboard.Key 支持的左右键位:")
    
    left_right_keys = [
        'shift_l', 'shift_r',
        'ctrl_l', 'ctrl_r', 
        'alt_l', 'alt_r',
        'cmd_l', 'cmd_r'
    ]
    
    for key_name in left_right_keys:
        if hasattr(keyboard.Key, key_name):
            print(f"  ✓ {key_name}: 支持")
        else:
            print(f"  ✗ {key_name}: 不支持")
    
    print("\n" + "=" * 50)
    test_key_detection()
