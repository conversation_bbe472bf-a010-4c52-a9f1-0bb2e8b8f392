#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask Web应用主文件
提供Web API和静态页面服务
"""

from flask import Flask, render_template, jsonify, request
from database import ClickDatabase
import json
from datetime import datetime, date, timedelta

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False  # 支持中文JSON输出

# 初始化数据库
db = ClickDatabase()

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/daily-stats')
def get_daily_stats():
    """获取每日统计数据API"""
    try:
        days = request.args.get('days', 30, type=int)
        stats = db.get_daily_stats(days)
        
        # 确保数据格式正确
        for stat in stats:
            if stat['stat_date']:
                # 确保日期格式一致
                stat['stat_date'] = str(stat['stat_date'])
        
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/keyboard-heatmap')
def get_keyboard_heatmap():
    """获取键盘热力图数据API"""
    try:
        days = request.args.get('days', 7, type=int)
        heatmap_data = db.get_keyboard_heatmap(days)
        
        return jsonify({
            'success': True,
            'data': heatmap_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/mouse-heatmap')
def get_mouse_heatmap():
    """获取鼠标热力图数据API"""
    try:
        days = request.args.get('days', 7, type=int)
        heatmap_data = db.get_mouse_heatmap(days)
        
        return jsonify({
            'success': True,
            'data': heatmap_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/summary')
def get_summary():
    """获取总体统计摘要"""
    try:
        # 获取今日统计
        today = date.today().isoformat()
        today_stats = db.get_daily_stats(1)
        
        # 获取最近7天的键盘和鼠标数据
        keyboard_data = db.get_keyboard_heatmap(7)
        mouse_data = db.get_mouse_heatmap(7)
        
        # 计算总点击数
        total_keyboard_today = today_stats[0]['total_keyboard_clicks'] if today_stats else 0
        total_mouse_today = today_stats[0]['total_mouse_clicks'] if today_stats else 0
        
        # 计算最活跃的键和鼠标按钮
        most_active_key = max(keyboard_data.items(), key=lambda x: x[1]) if keyboard_data else ('无', 0)
        most_active_mouse = max(mouse_data.items(), key=lambda x: x[1]) if mouse_data else ('无', 0)
        
        summary = {
            'today_keyboard_clicks': total_keyboard_today,
            'today_mouse_clicks': total_mouse_today,
            'today_total_clicks': total_keyboard_today + total_mouse_today,
            'most_active_key': {
                'key': most_active_key[0],
                'count': most_active_key[1]
            },
            'most_active_mouse_button': {
                'button': most_active_mouse[0],
                'count': most_active_mouse[1]
            },
            'total_keys_used': len(keyboard_data),
            'total_mouse_buttons_used': len(mouse_data)
        }
        
        return jsonify({
            'success': True,
            'data': summary
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-data')
def generate_test_data():
    """生成测试数据（仅用于开发测试）"""
    try:
        import random
        from datetime import datetime, timedelta
        
        # 生成最近7天的测试数据
        keys = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 
                'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
                'space', 'enter', 'backspace', 'shift', 'ctrl', 'alt']
        
        mouse_buttons = ['left', 'right', 'middle', 'scroll_up', 'scroll_down']
        
        for i in range(7):
            test_date = (date.today() - timedelta(days=i)).isoformat()
            
            # 生成随机键盘点击
            for _ in range(random.randint(50, 200)):
                key = random.choice(keys)
                db.record_keyboard_click(key)
            
            # 生成随机鼠标点击
            for _ in range(random.randint(20, 100)):
                button = random.choice(mouse_buttons)
                x = random.randint(0, 1920)
                y = random.randint(0, 1080)
                db.record_mouse_click(button, x, y)
        
        return jsonify({
            'success': True,
            'message': '测试数据生成完成'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        'success': False,
        'error': '页面未找到'
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        'success': False,
        'error': '服务器内部错误'
    }), 500

if __name__ == '__main__':
    print("启动键盘鼠标统计Web服务...")
    print("访问地址: http://localhost:8073")
    print("按 Ctrl+C 停止服务")
    
    # 启动Flask应用
    app.run(
        host='0.0.0.0',
        port=8073,
        debug=True,
        threaded=True
    )
