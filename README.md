# 键盘鼠标点击统计系统

一个基于Python的键盘和鼠标点击统计系统，可以实时监控用户的键盘和鼠标使用情况，并通过Web界面展示统计数据。

## 功能特性

- 🖱️ 实时监控键盘和鼠标点击
- 📊 每日点击统计图表
- 🎨 键盘和鼠标热力图可视化
- 💾 SQLite数据库存储
- 🌐 Web界面访问 (端口8073)
- 📱 响应式设计，支持移动端

## 项目结构

```
keyborad-analysis/
├── app.py              # Flask Web应用主文件
├── monitor.py          # 键盘鼠标监听服务
├── database.py         # 数据库操作模块
├── requirements.txt    # Python依赖包
├── static/            # 静态文件目录
│   ├── css/
│   ├── js/
│   └── images/
├── templates/         # HTML模板目录
└── data/             # 数据库文件目录
```

## 安装和运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 初始化数据库：
```bash
python database.py
```

3. 启动监听服务（后台运行）：
```bash
python monitor.py &
```

4. 启动Web服务：
```bash
python app.py
```

5. 访问统计页面：
打开浏览器访问 http://localhost:8073

## 注意事项

- 首次运行需要授权键盘和鼠标监听权限
- 建议在虚拟环境中运行
- 数据库文件保存在 `data/` 目录下
