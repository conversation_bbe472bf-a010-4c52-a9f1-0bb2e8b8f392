# 键盘鼠标点击统计系统 - 使用说明

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动系统
```bash
# 方式一：使用启动脚本（推荐）
python3 start.py

# 方式二：分别启动服务
python3 database.py    # 初始化数据库
python3 monitor.py &   # 启动监听服务（后台）
python3 app.py         # 启动Web服务
```

### 3. 访问统计页面
打开浏览器访问：http://localhost:8073

## 功能说明

### 📊 统计功能
- **实时监控**：自动记录键盘和鼠标的每次点击
- **每日统计**：显示每天的点击数量趋势图
- **热力图**：可视化显示最常用的按键和鼠标按钮
- **数据存储**：所有数据保存在SQLite数据库中

### 🎨 界面功能
- **响应式设计**：支持桌面和移动设备
- **实时刷新**：点击刷新按钮获取最新数据
- **时间范围**：可选择查看7天、14天或30天的数据
- **颜色编码**：使用不同颜色表示点击频率

### 🔧 高级功能
- **后台运行**：监听服务可在后台持续运行
- **数据导出**：通过API接口获取原始数据
- **测试数据**：内置测试数据生成功能

## API接口

### 获取统计摘要
```
GET /api/summary
```

### 获取每日统计
```
GET /api/daily-stats?days=30
```

### 获取键盘热力图
```
GET /api/keyboard-heatmap?days=7
```

### 获取鼠标热力图
```
GET /api/mouse-heatmap?days=7
```

### 生成测试数据
```
GET /api/test-data
```

## 文件结构

```
keyborad-analysis/
├── app.py              # Flask Web应用
├── monitor.py          # 键盘鼠标监听服务
├── database.py         # 数据库操作模块
├── start.py           # 系统启动脚本
├── requirements.txt    # Python依赖
├── README.md          # 项目说明
├── 使用说明.md        # 详细使用说明
├── static/            # 静态文件
│   ├── css/style.css  # 样式文件
│   └── js/app.js      # 前端逻辑
├── templates/         # HTML模板
│   └── index.html     # 主页面
└── data/             # 数据文件
    └── clicks.db     # SQLite数据库
```

## 注意事项

### 权限要求
- **Linux/macOS**：首次运行需要授权键盘和鼠标监听权限
- **Windows**：可能需要以管理员身份运行

### 性能优化
- 监听服务占用资源很少，可长期后台运行
- 数据库会自动优化，无需手动维护
- Web服务仅在需要时启动

### 数据安全
- 所有数据仅保存在本地
- 不会上传任何个人信息
- 可随时删除数据库文件清空数据

## 故障排除

### 常见问题

1. **监听服务无法启动**
   - 检查是否有权限访问键盘和鼠标
   - 确认pynput库安装正确

2. **Web页面无法访问**
   - 检查8073端口是否被占用
   - 确认Flask服务正常启动

3. **数据不更新**
   - 检查监听服务是否在运行
   - 确认数据库文件权限正常

### 日志查看
```bash
# 查看监听服务日志
tail -f data/monitor.log

# 查看Web服务日志
python3 app.py  # 直接运行查看输出
```

## 自定义配置

### 修改端口
编辑 `app.py` 文件中的端口设置：
```python
app.run(port=8073)  # 修改为其他端口
```

### 修改数据库位置
编辑 `database.py` 文件中的数据库路径：
```python
def __init__(self, db_path: str = "data/clicks.db"):
```

### 自定义键盘布局
编辑 `static/js/app.js` 文件中的键盘布局定义。

## 技术支持

如果遇到问题，请检查：
1. Python版本（建议3.8+）
2. 依赖包版本
3. 操作系统权限设置
4. 防火墙和端口设置

祝您使用愉快！ 🎉
