#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统启动脚本
用于启动键盘鼠标监听服务和Web服务
"""

import os
import sys
import time
import subprocess
import threading
import signal
from database import ClickDatabase

class SystemManager:
    def __init__(self):
        self.monitor_process = None
        self.web_process = None
        self.running = True
        
        # 设置信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出"""
        print(f"\n收到信号 {signum}，正在停止所有服务...")
        self.stop_all_services()
        sys.exit(0)
    
    def init_database(self):
        """初始化数据库"""
        print("正在初始化数据库...")
        try:
            db = ClickDatabase()
            print("✓ 数据库初始化完成")
            return True
        except Exception as e:
            print(f"✗ 数据库初始化失败: {e}")
            return False
    
    def start_monitor_service(self):
        """启动监听服务"""
        print("正在启动键盘鼠标监听服务...")
        try:
            self.monitor_process = subprocess.Popen([
                sys.executable, 'monitor.py', '-v'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待一下确保服务启动
            time.sleep(2)
            
            if self.monitor_process.poll() is None:
                print("✓ 监听服务启动成功")
                return True
            else:
                print("✗ 监听服务启动失败")
                return False
        except Exception as e:
            print(f"✗ 启动监听服务失败: {e}")
            return False
    
    def start_web_service(self):
        """启动Web服务"""
        print("正在启动Web服务...")
        try:
            self.web_process = subprocess.Popen([
                sys.executable, 'app.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待一下确保服务启动
            time.sleep(3)
            
            if self.web_process.poll() is None:
                print("✓ Web服务启动成功")
                print("✓ 访问地址: http://localhost:8073")
                return True
            else:
                print("✗ Web服务启动失败")
                return False
        except Exception as e:
            print(f"✗ 启动Web服务失败: {e}")
            return False
    
    def stop_all_services(self):
        """停止所有服务"""
        self.running = False
        
        if self.monitor_process:
            print("正在停止监听服务...")
            self.monitor_process.terminate()
            try:
                self.monitor_process.wait(timeout=5)
                print("✓ 监听服务已停止")
            except subprocess.TimeoutExpired:
                self.monitor_process.kill()
                print("✓ 监听服务已强制停止")
        
        if self.web_process:
            print("正在停止Web服务...")
            self.web_process.terminate()
            try:
                self.web_process.wait(timeout=5)
                print("✓ Web服务已停止")
            except subprocess.TimeoutExpired:
                self.web_process.kill()
                print("✓ Web服务已强制停止")
    
    def check_dependencies(self):
        """检查依赖包"""
        print("正在检查依赖包...")
        try:
            import flask
            import pynput
            print("✓ 所有依赖包已安装")
            return True
        except ImportError as e:
            print(f"✗ 缺少依赖包: {e}")
            print("请运行: pip install -r requirements.txt")
            return False
    
    def generate_test_data(self):
        """生成测试数据"""
        print("是否生成测试数据？(y/n): ", end='')
        choice = input().lower().strip()
        
        if choice == 'y':
            print("正在生成测试数据...")
            try:
                import requests
                response = requests.get('http://localhost:8073/api/test-data')
                if response.status_code == 200:
                    print("✓ 测试数据生成完成")
                else:
                    print("✗ 测试数据生成失败")
            except Exception as e:
                print(f"✗ 生成测试数据时出错: {e}")
    
    def start_system(self):
        """启动整个系统"""
        print("=" * 50)
        print("键盘鼠标点击统计系统")
        print("=" * 50)
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 初始化数据库
        if not self.init_database():
            return False
        
        # 启动监听服务
        if not self.start_monitor_service():
            return False
        
        # 启动Web服务
        if not self.start_web_service():
            self.stop_all_services()
            return False
        
        print("\n" + "=" * 50)
        print("✓ 系统启动完成！")
        print("✓ 监听服务正在后台运行")
        print("✓ Web界面: http://localhost:8073")
        print("✓ 按 Ctrl+C 停止系统")
        print("=" * 50)
        
        # 询问是否生成测试数据
        # self.generate_test_data()
        
        # 保持运行
        try:
            while self.running:
                time.sleep(1)
                
                # 检查进程状态
                if self.monitor_process and self.monitor_process.poll() is not None:
                    print("⚠ 监听服务意外停止")
                    break
                
                if self.web_process and self.web_process.poll() is not None:
                    print("⚠ Web服务意外停止")
                    break
                    
        except KeyboardInterrupt:
            print("\n用户中断，正在停止系统...")
        finally:
            self.stop_all_services()
        
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='键盘鼠标统计系统启动器')
    parser.add_argument('--monitor-only', action='store_true',
                       help='只启动监听服务')
    parser.add_argument('--web-only', action='store_true',
                       help='只启动Web服务')
    
    args = parser.parse_args()
    
    manager = SystemManager()
    
    if args.monitor_only:
        manager.check_dependencies()
        manager.init_database()
        manager.start_monitor_service()
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            manager.stop_all_services()
    elif args.web_only:
        manager.check_dependencies()
        manager.init_database()
        manager.start_web_service()
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            manager.stop_all_services()
    else:
        manager.start_system()

if __name__ == "__main__":
    main()
