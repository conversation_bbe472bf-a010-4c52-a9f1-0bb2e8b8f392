#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清理脚本
用于清空测试数据，重置统计系统
"""

import os
import sys
from database import ClickDatabase

def main():
    """主函数"""
    print("=" * 50)
    print("键盘鼠标统计系统 - 数据清理工具")
    print("=" * 50)
    
    # 检查数据库文件是否存在
    db_path = "data/clicks.db"
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在，无需清理")
        return
    
    # 初始化数据库连接
    try:
        db = ClickDatabase()
        print("✅ 数据库连接成功")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return
    
    # 显示当前数据概览
    print("\n📊 当前数据概览:")
    summary = db.get_data_summary()
    if summary:
        print(f"  - 键盘点击记录: {summary['keyboard_clicks']:,} 条")
        print(f"  - 鼠标点击记录: {summary['mouse_clicks']:,} 条")
        print(f"  - 每日统计记录: {summary['daily_stats']:,} 条")
        if summary['date_range']['start'] and summary['date_range']['end']:
            print(f"  - 数据时间范围: {summary['date_range']['start']} 到 {summary['date_range']['end']}")
        else:
            print("  - 数据时间范围: 无数据")
        
        total_records = summary['keyboard_clicks'] + summary['mouse_clicks'] + summary['daily_stats']
        if total_records == 0:
            print("\n✅ 数据库已经是空的，无需清理")
            return
    else:
        print("  ❌ 无法获取数据概览")
        return
    
    # 确认清理操作
    print(f"\n⚠️  警告: 即将清空所有统计数据 (共 {total_records:,} 条记录)")
    print("此操作不可撤销！")
    
    while True:
        choice = input("\n是否确认清空所有数据？(yes/no): ").lower().strip()
        if choice in ['yes', 'y']:
            break
        elif choice in ['no', 'n']:
            print("❌ 操作已取消")
            return
        else:
            print("请输入 'yes' 或 'no'")
    
    # 执行清理操作
    print("\n🧹 正在清理数据...")
    try:
        if db.clear_all_data():
            print("✅ 数据清理完成！")
            
            # 验证清理结果
            new_summary = db.get_data_summary()
            if new_summary:
                total_remaining = (new_summary['keyboard_clicks'] + 
                                 new_summary['mouse_clicks'] + 
                                 new_summary['daily_stats'])
                if total_remaining == 0:
                    print("✅ 验证通过：所有数据已成功清空")
                else:
                    print(f"⚠️  警告：仍有 {total_remaining} 条记录未清理")
            
            print("\n📝 清理完成后的状态:")
            print("  - 数据库表结构保持完整")
            print("  - 所有统计数据已清空")
            print("  - 可以开始收集新的真实数据")
            print("\n💡 提示:")
            print("  - 重启监听服务开始收集新数据: python3 monitor.py")
            print("  - 访问Web界面查看实时统计: http://localhost:8073")
            
        else:
            print("❌ 数据清理失败")
            
    except Exception as e:
        print(f"❌ 清理过程中出现错误: {e}")

if __name__ == "__main__":
    main()
