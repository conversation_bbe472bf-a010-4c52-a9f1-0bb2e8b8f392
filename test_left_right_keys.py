#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试左右键位区分功能
生成一些测试数据来验证左右键位是否能正确区分和显示
"""

from database import ClickDatabase
import random

def generate_left_right_test_data():
    """生成左右键位测试数据"""
    db = ClickDatabase()
    
    print("正在生成左右键位测试数据...")
    
    # 定义左右键位对
    left_right_keys = [
        ('shift_l', 'shift_r'),
        ('ctrl_l', 'ctrl_r'),
        ('alt_l', 'alt_r'),
        ('cmd_l', 'cmd_r')
    ]
    
    # 为每个左右键位对生成不同的点击数据
    for left_key, right_key in left_right_keys:
        # 左键通常使用更频繁
        left_clicks = random.randint(50, 150)
        right_clicks = random.randint(20, 80)
        
        print(f"  {left_key}: {left_clicks} 次点击")
        print(f"  {right_key}: {right_clicks} 次点击")
        
        # 记录左键点击
        for _ in range(left_clicks):
            db.record_keyboard_click(left_key)
        
        # 记录右键点击
        for _ in range(right_clicks):
            db.record_keyboard_click(right_key)
    
    # 添加一些其他常用键的数据
    other_keys = ['space', 'enter', 'backspace', 'tab', 'a', 's', 'd', 'w']
    for key in other_keys:
        clicks = random.randint(30, 100)
        print(f"  {key}: {clicks} 次点击")
        for _ in range(clicks):
            db.record_keyboard_click(key)
    
    print("✅ 左右键位测试数据生成完成！")
    print("\n📊 数据概览:")
    
    # 显示生成的数据概览
    summary = db.get_data_summary()
    if summary:
        print(f"  - 总键盘点击记录: {summary['keyboard_clicks']:,} 条")
        print(f"  - 数据时间范围: {summary['date_range']['start']} 到 {summary['date_range']['end']}")
    
    print("\n💡 测试说明:")
    print("  - 左键位有蓝色左边框标识")
    print("  - 右键位有红色右边框标识")
    print("  - 左键位通常点击次数更多")
    print("  - 访问 http://localhost:8073 查看效果")

def show_keyboard_heatmap_data():
    """显示键盘热力图数据"""
    db = ClickDatabase()
    
    print("\n🔥 当前键盘热力图数据:")
    heatmap_data = db.get_keyboard_heatmap(7)
    
    if not heatmap_data:
        print("  无数据")
        return
    
    # 按点击次数排序显示
    sorted_data = sorted(heatmap_data.items(), key=lambda x: x[1], reverse=True)
    
    for key, count in sorted_data:
        if count > 0:
            if key.endswith('_l'):
                print(f"  🔵 {key}: {count:,} 次 (左键位)")
            elif key.endswith('_r'):
                print(f"  🔴 {key}: {count:,} 次 (右键位)")
            else:
                print(f"  ⚪ {key}: {count:,} 次")

def main():
    """主函数"""
    print("=" * 60)
    print("左右键位区分功能测试")
    print("=" * 60)
    
    db = ClickDatabase()
    
    # 检查当前数据
    summary = db.get_data_summary()
    if summary and summary['keyboard_clicks'] > 0:
        print(f"⚠️  当前已有 {summary['keyboard_clicks']:,} 条键盘数据")
        choice = input("是否清空现有数据并生成新的测试数据？(y/n): ").lower().strip()
        if choice == 'y':
            db.clear_all_data()
            print("✅ 现有数据已清空")
        else:
            print("保留现有数据，添加新的测试数据")
    
    # 生成测试数据
    generate_left_right_test_data()
    
    # 显示数据概览
    show_keyboard_heatmap_data()
    
    print("\n🚀 测试完成！")
    print("现在可以启动Web服务查看左右键位的区分效果：")
    print("  python3 app.py")

if __name__ == "__main__":
    main()
