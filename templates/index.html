<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>键盘鼠标点击统计</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-chart-line"></i> 键盘鼠标点击统计</h1>
            <div class="header-controls">
                <button id="refreshBtn" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> 刷新数据
                </button>
                <select id="daySelector" class="day-selector">
                    <option value="7">最近7天</option>
                    <option value="14">最近14天</option>
                    <option value="30" selected>最近30天</option>
                </select>
            </div>
        </header>

        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="stat-content">
                    <h3 id="todayKeyboard">0</h3>
                    <p>今日键盘点击</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-mouse"></i>
                </div>
                <div class="stat-content">
                    <h3 id="todayMouse">0</h3>
                    <p>今日鼠标点击</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-hand-pointer"></i>
                </div>
                <div class="stat-content">
                    <h3 id="todayTotal">0</h3>
                    <p>今日总点击</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                    <h3 id="mostActiveKey">-</h3>
                    <p>最活跃按键</p>
                </div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <div class="chart-container">
                <h2><i class="fas fa-chart-bar"></i> 每日点击趋势</h2>
                <div class="chart-wrapper">
                    <canvas id="dailyChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 热力图区域 -->
        <div class="heatmap-section">
            <div class="heatmap-container">
                <h2><i class="fas fa-keyboard"></i> 键盘热力图</h2>
                <div id="keyboardHeatmap" class="keyboard-layout"></div>
            </div>
            
            <div class="heatmap-container">
                <h2><i class="fas fa-mouse"></i> 鼠标热力图</h2>
                <div id="mouseHeatmap" class="mouse-layout"></div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="loading-indicator">
            <div class="spinner"></div>
            <p>正在加载数据...</p>
        </div>

        <!-- 错误提示 -->
        <div id="errorMessage" class="error-message" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <span id="errorText"></span>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
