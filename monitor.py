#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
键盘鼠标监听服务
使用pynput库监听键盘和鼠标事件，并记录到数据库
"""

import time
import threading
from pynput import keyboard, mouse
from database import ClickDatabase
import signal
import sys
import os

class InputMonitor:
    def __init__(self):
        """初始化监听器"""
        self.db = ClickDatabase()
        self.running = True
        self.keyboard_listener = None
        self.mouse_listener = None
        
        # 设置信号处理器，用于优雅退出
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出"""
        print(f"\n收到信号 {signum}，正在停止监听...")
        self.stop()
        sys.exit(0)
    
    def on_key_press(self, key):
        """键盘按键按下事件处理"""
        try:
            # 处理普通字符键
            if hasattr(key, 'char') and key.char is not None:
                key_name = key.char.lower()
            else:
                # 处理特殊键，区分左右键位
                key_str = str(key)
                if key_str.startswith('Key.'):
                    key_name = key_str.replace('Key.', '')
                else:
                    key_name = key_str

                # 区分左右对称键位
                if hasattr(key, 'name'):
                    if key.name in ['shift_l', 'shift_r', 'ctrl_l', 'ctrl_r', 'alt_l', 'alt_r', 'cmd_l', 'cmd_r']:
                        key_name = key.name
                    elif key_name in ['shift', 'ctrl', 'alt', 'cmd']:
                        # 如果无法区分左右，尝试通过其他方式
                        try:
                            # 检查按键的具体类型
                            if hasattr(key, '_name'):
                                if 'left' in key._name.lower() or '_l' in key._name.lower():
                                    key_name = f"{key_name}_l"
                                elif 'right' in key._name.lower() or '_r' in key._name.lower():
                                    key_name = f"{key_name}_r"
                        except:
                            pass

            # 记录到数据库
            self.db.record_keyboard_click(key_name)
            print(f"键盘: {key_name}")

        except Exception as e:
            print(f"键盘事件处理错误: {e}")
    
    def on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件处理"""
        if pressed:  # 只记录按下事件，避免重复
            try:
                button_name = str(button).replace('Button.', '')
                self.db.record_mouse_click(button_name, x, y)
                print(f"鼠标: {button_name} at ({x}, {y})")
            except Exception as e:
                print(f"鼠标点击事件处理错误: {e}")
    
    def on_mouse_scroll(self, x, y, dx, dy):
        """鼠标滚轮事件处理"""
        try:
            if dy > 0:
                scroll_direction = "scroll_up"
            else:
                scroll_direction = "scroll_down"
            
            self.db.record_mouse_click(scroll_direction, x, y)
            print(f"鼠标滚轮: {scroll_direction} at ({x}, {y})")
        except Exception as e:
            print(f"鼠标滚轮事件处理错误: {e}")
    
    def start_monitoring(self):
        """开始监听键盘和鼠标事件"""
        print("开始监听键盘和鼠标事件...")
        print("按 Ctrl+C 停止监听")
        
        try:
            # 启动键盘监听器
            self.keyboard_listener = keyboard.Listener(
                on_press=self.on_key_press
            )
            self.keyboard_listener.start()
            
            # 启动鼠标监听器
            self.mouse_listener = mouse.Listener(
                on_click=self.on_mouse_click,
                on_scroll=self.on_mouse_scroll
            )
            self.mouse_listener.start()
            
            # 保持程序运行
            while self.running:
                time.sleep(1)
                
        except Exception as e:
            print(f"监听启动错误: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止监听"""
        self.running = False
        
        if self.keyboard_listener:
            self.keyboard_listener.stop()
            print("键盘监听已停止")
        
        if self.mouse_listener:
            self.mouse_listener.stop()
            print("鼠标监听已停止")

def run_as_daemon():
    """以守护进程方式运行"""
    try:
        pid = os.fork()
        if pid > 0:
            # 父进程退出
            sys.exit(0)
    except OSError as e:
        print(f"fork失败: {e}")
        sys.exit(1)
    
    # 子进程继续执行
    os.chdir("/")
    os.setsid()
    os.umask(0)
    
    # 第二次fork
    try:
        pid = os.fork()
        if pid > 0:
            sys.exit(0)
    except OSError as e:
        print(f"第二次fork失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='键盘鼠标点击监听服务')
    parser.add_argument('-d', '--daemon', action='store_true', 
                       help='以守护进程方式运行')
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='显示详细输出')
    
    args = parser.parse_args()
    
    if args.daemon:
        print("以守护进程方式启动...")
        run_as_daemon()
    
    # 创建监听器实例
    monitor = InputMonitor()
    
    if not args.verbose:
        # 重定向输出到日志文件
        log_file = open('data/monitor.log', 'a')
        sys.stdout = log_file
        sys.stderr = log_file
    
    try:
        monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
    except Exception as e:
        print(f"监听服务错误: {e}")
    finally:
        monitor.stop()

if __name__ == "__main__":
    main()
