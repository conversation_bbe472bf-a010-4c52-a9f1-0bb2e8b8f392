#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库操作模块
负责SQLite数据库的创建、初始化和数据操作
"""

import sqlite3
import os
from datetime import datetime, date
from typing import List, Dict, Tuple, Optional

class ClickDatabase:
    def __init__(self, db_path: str = "data/clicks.db"):
        """初始化数据库连接"""
        self.db_path = db_path
        # 确保数据目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
        return conn
    
    def init_database(self):
        """初始化数据库表结构"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 创建键盘点击记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS keyboard_clicks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key_name TEXT NOT NULL,
                click_date DATE NOT NULL,
                click_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                click_count INTEGER DEFAULT 1
            )
        ''')
        
        # 创建鼠标点击记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mouse_clicks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                button_name TEXT NOT NULL,  -- 'left', 'right', 'middle', 'scroll_up', 'scroll_down'
                click_date DATE NOT NULL,
                click_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                click_count INTEGER DEFAULT 1,
                x_position INTEGER,
                y_position INTEGER
            )
        ''')
        
        # 创建每日统计汇总表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS daily_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stat_date DATE NOT NULL UNIQUE,
                total_keyboard_clicks INTEGER DEFAULT 0,
                total_mouse_clicks INTEGER DEFAULT 0,
                most_used_key TEXT,
                most_used_mouse_button TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引以提高查询性能
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyboard_date ON keyboard_clicks(click_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_keyboard_key ON keyboard_clicks(key_name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mouse_date ON mouse_clicks(click_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_mouse_button ON mouse_clicks(button_name)')
        
        conn.commit()
        conn.close()
        print("数据库初始化完成")
    
    def record_keyboard_click(self, key_name: str):
        """记录键盘点击"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        today = date.today().isoformat()
        
        cursor.execute('''
            INSERT INTO keyboard_clicks (key_name, click_date)
            VALUES (?, ?)
        ''', (key_name, today))
        
        conn.commit()
        conn.close()
        
        # 更新每日统计
        self.update_daily_stats(today)
    
    def record_mouse_click(self, button_name: str, x: int = None, y: int = None):
        """记录鼠标点击"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        today = date.today().isoformat()
        
        cursor.execute('''
            INSERT INTO mouse_clicks (button_name, click_date, x_position, y_position)
            VALUES (?, ?, ?, ?)
        ''', (button_name, today, x, y))
        
        conn.commit()
        conn.close()
        
        # 更新每日统计
        self.update_daily_stats(today)
    
    def update_daily_stats(self, stat_date: str):
        """更新每日统计数据"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # 统计当日键盘点击总数
        cursor.execute('''
            SELECT COUNT(*) as total_keyboard
            FROM keyboard_clicks 
            WHERE click_date = ?
        ''', (stat_date,))
        total_keyboard = cursor.fetchone()['total_keyboard']
        
        # 统计当日鼠标点击总数
        cursor.execute('''
            SELECT COUNT(*) as total_mouse
            FROM mouse_clicks 
            WHERE click_date = ?
        ''', (stat_date,))
        total_mouse = cursor.fetchone()['total_mouse']
        
        # 找出最常用的键
        cursor.execute('''
            SELECT key_name, COUNT(*) as count
            FROM keyboard_clicks 
            WHERE click_date = ?
            GROUP BY key_name
            ORDER BY count DESC
            LIMIT 1
        ''', (stat_date,))
        most_used_key_result = cursor.fetchone()
        most_used_key = most_used_key_result['key_name'] if most_used_key_result else None
        
        # 找出最常用的鼠标按键
        cursor.execute('''
            SELECT button_name, COUNT(*) as count
            FROM mouse_clicks 
            WHERE click_date = ?
            GROUP BY button_name
            ORDER BY count DESC
            LIMIT 1
        ''', (stat_date,))
        most_used_mouse_result = cursor.fetchone()
        most_used_mouse = most_used_mouse_result['button_name'] if most_used_mouse_result else None
        
        # 插入或更新每日统计
        cursor.execute('''
            INSERT OR REPLACE INTO daily_stats 
            (stat_date, total_keyboard_clicks, total_mouse_clicks, most_used_key, most_used_mouse_button, updated_at)
            VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (stat_date, total_keyboard, total_mouse, most_used_key, most_used_mouse))
        
        conn.commit()
        conn.close()
    
    def get_daily_stats(self, days: int = 30) -> List[Dict]:
        """获取最近N天的统计数据"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM daily_stats 
            ORDER BY stat_date DESC 
            LIMIT ?
        ''', (days,))
        
        results = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return results
    
    def get_keyboard_heatmap(self, days: int = 7) -> Dict[str, int]:
        """获取键盘热力图数据（最近N天）"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT key_name, COUNT(*) as click_count
            FROM keyboard_clicks 
            WHERE click_date >= date('now', '-{} days')
            GROUP BY key_name
            ORDER BY click_count DESC
        '''.format(days))
        
        results = {row['key_name']: row['click_count'] for row in cursor.fetchall()}
        conn.close()
        return results
    
    def get_mouse_heatmap(self, days: int = 7) -> Dict[str, int]:
        """获取鼠标热力图数据（最近N天）"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT button_name, COUNT(*) as click_count
            FROM mouse_clicks 
            WHERE click_date >= date('now', '-{} days')
            GROUP BY button_name
            ORDER BY click_count DESC
        '''.format(days))
        
        results = {row['button_name']: row['click_count'] for row in cursor.fetchall()}
        conn.close()
        return results

if __name__ == "__main__":
    # 测试数据库初始化
    db = ClickDatabase()
    print("数据库测试完成")
